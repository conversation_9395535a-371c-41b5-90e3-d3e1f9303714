import { useState, useEffect } from "react";
import { PrimaryButton } from "../Utils/Button";
import { CiCircleChevUp } from "react-icons/ci";

export const ScrollToTopButton = () => {
  const [visible, setVisible] = useState(false);
  const [isFooterVisible, setFooterVisible] = useState(false);

  useEffect(() => {
    const footer = document.querySelector("footer");
    if (!footer) return;

    const observer = new IntersectionObserver((entries) => setFooterVisible(entries[0].isIntersecting), {
      threshold: 0.1,
    });

    observer.observe(footer);
    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    const toggleVisibility = () => setVisible(window.scrollY > 200);
    window.addEventListener("scroll", toggleVisibility);
    return () => window.removeEventListener("scroll", toggleVisibility);
  }, []);

  const scrollToTop = () => window.scrollTo({ top: 0, behavior: "smooth" });

  return (
    visible && (
      <PrimaryButton
        onClick={scrollToTop}
        className={`fixed right-5 z-50 flex items-center justify-center rounded-full p-0.5 transition-all duration-300 ${isFooterVisible ? "bottom-15" : "bottom-5"}`}
      >
        <CiCircleChevUp className="text-2xl font-extrabold" />
      </PrimaryButton>
    )
  );
};
