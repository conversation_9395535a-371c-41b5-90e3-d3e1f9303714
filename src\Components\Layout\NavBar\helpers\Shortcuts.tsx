import { <PERSON> } from "react-router-dom";
import { FaGithub } from "react-icons/fa";
import { SiSunrise } from "react-icons/si";
import { ThemeButton } from "./ThemeButton";
import { VerticalSeparator } from "../../../Utils/Separator";
import { TextSecondary, TextTertiary, TextPrimary } from "../../../Utils/Text";

export const Shortcuts = () => {
  return (
    <div className="flex items-center gap-3">
      <div className="flex flex-row items-center gap-2">
        <Link to="/" className="flex items-center gap-2">
          <TextSecondary>{<FaGithub />}</TextSecondary>
          <div className="text-[12px] font-semibold">
            <TextTertiary>93.2k</TextTertiary>
          </div>
        </Link>
      </div>

      <TextTertiary>{<VerticalSeparator />}</TextTertiary>

      <TextPrimary>{<ThemeButton />}</TextPrimary>

      <TextTertiary>{<VerticalSeparator />}</TextTertiary>

      <Link to="/signIn" className="text-lg">
        <TextPrimary>{<SiSunrise />}</TextPrimary>
      </Link>
    </div>
  );
};
