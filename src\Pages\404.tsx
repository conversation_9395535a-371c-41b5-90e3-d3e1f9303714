import { useTheme } from "../Context/Theme";
import { TextPrimary } from "../Components/Utils/Text";

export const _404Page = () => {
  const { theme } = useTheme();
  const isDark = theme === "dark";
  return (
    <div className="mt-[calc(20vh)] flex flex-col items-center justify-center gap-5">
      <img src={`/images/404-${theme}.png`} alt="404" className="h-40" />
      <TextPrimary className="text-xl">404 : Page / Route Not Found</TextPrimary>
    </div>
  );
};
