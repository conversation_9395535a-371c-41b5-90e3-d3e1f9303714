import { Link } from "react-router-dom";
import { Bi<PERSON>opyright } from "react-icons/bi";
import { TextPrimary, TextTertiary } from "../Utils/Text";
import { CircleSeparator, VerticalSeparator } from "../Utils/Separator";

export const Footer = () => {
  const line1 = (
    <TextPrimary className="flex flex-row items-center justify-center gap-2 text-center text-sm font-extralight">
      <BiCopyright />
      {new Date().getFullYear().toString()}
      <CircleSeparator />
      1sT - Services
      <CircleSeparator />
      All rights reserved
    </TextPrimary>
  );

  const line2 = (
    <TextTertiary className="flex flex-row items-center justify-center gap-2 text-center text-xs">
      <Link to="/tos">Terms of Service</Link>
      <VerticalSeparator />
      <Link to="/privacyPolicy">Privacy Policy</Link>
    </TextTertiary>
  );

  return (
    <footer className="absolute bottom-0 z-10 w-full">
      <div className="flex flex-col items-center justify-center gap-1 px-6 py-4">
        {line1}
        {line2}
      </div>
    </footer>
  );
};
