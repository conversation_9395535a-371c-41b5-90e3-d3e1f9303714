import { HiX, HiMenu } from "react-icons/hi";
import { TextPrimary } from "../../Utils/Text";
import { useNavigate } from "react-router-dom";
import { Shortcuts } from "./helpers/Shortcuts";
import { useTheme } from "../../../Context/Theme";
import { useState, useEffect, useRef } from "react";
import { BackgroundColor, tabs } from "../../../constants";

export const MobileNavbar = () => {
  const { theme } = useTheme();
  const navigate = useNavigate();
  const isDark = theme === "dark";
  const navRef = useRef<HTMLDivElement>(null);
  const [scrolled, setScrolled] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      if (navRef.current && !navRef.current.contains(event.target as Node)) setIsMenuOpen(false);
    };

    if (!isMenuOpen) return;

    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("touchstart", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("touchstart", handleClickOutside);
    };
  }, [isMenuOpen]);

  useEffect(() => setIsMenuOpen(false), [location.pathname]);

  useEffect(() => {
    const toggleVisibility = () => setScrolled(window.scrollY > 0);
    window.addEventListener("scroll", toggleVisibility);
    return () => window.removeEventListener("scroll", toggleVisibility);
  }, []);

  const Logo = (
    <button onClick={() => navigate("/")} className="flex cursor-pointer items-center p-2 text-xl">
      <img src={`/images/logo-${theme}.png`} alt="logo" className="h-8 w-8" />
      <TextPrimary className="font-bold">1sT Services</TextPrimary>
    </button>
  );

  const MenuIcon = (
    <button onClick={() => setIsMenuOpen(!isMenuOpen)} className="text-3xl">
      {isMenuOpen ? <TextPrimary>{<HiX />}</TextPrimary> : <TextPrimary>{<HiMenu />}</TextPrimary>}
    </button>
  );

  const NavTabs = tabs.map((tab, index) => (
    <button
      key={index}
      onClick={() => navigate(`/${tab.toLowerCase()}`)}
      className="flex w-full cursor-pointer flex-row items-center gap-5 px-5"
    >
      <TextPrimary>{tab}</TextPrimary>
    </button>
  ));

  return (
    <div
      ref={navRef}
      className={
        "fixed top-0 z-10 flex h-12 w-full flex-row items-center justify-between p-7 px-2 md:hidden " +
        `bg-[${BackgroundColor[theme]}]` +
        (scrolled ? (theme === "dark" ? " border-b border-[#212121] shadow-xl" : isMenuOpen ? "" : " shadow-md") : "")
      }
    >
      {Logo}
      {MenuIcon}

      {isMenuOpen && (
        <div
          className={
            "absolute top-12 left-0 flex w-full flex-col items-start gap-6 py-6 shadow-xl md:hidden " +
            `bg-[${BackgroundColor[theme]}]`
          }
        >
          {NavTabs}

          <div className="flex w-full flex-row items-center justify-center">
            <Shortcuts />
          </div>
        </div>
      )}
    </div>
  );
};
