import React from "react";
import { services } from "../constants";
import { useTheme } from "../Context/Theme";
import { IoMdPersonAdd } from "react-icons/io";
import { VerticalSeparator } from "../Components/Utils/Separator";
import { MdNewReleases, MdOutlineAddShoppingCart } from "react-icons/md";
import { PrimaryButton, SecondaryButton } from "../Components/Utils/Button";
import { TextPrimary, TextSecondary, TextTertiary } from "../Components/Utils/Text";

const marqueeText = (
  <div className="flex items-center gap-2">
    {services.map((service) => (
      <React.Fragment>
        <TextSecondary className="text-sm font-semibold">{service}</TextSecondary>
        <TextSecondary className="opacity-50">{<VerticalSeparator />}</TextSecondary>
      </React.Fragment>
    ))}
  </div>
);

export const HomePage = () => {
  const { theme } = useTheme();
  const isDark = theme === "dark";

  return (
    <div className="flex flex-col items-center justify-center gap-10 px-2 pt-8 md:px-5">
      <div
        className={
          "flex flex-row items-center gap-2 rounded-4xl px-3 py-1 text-sm " + (isDark ? "bg-[#171717]" : "bg-[#dedede]")
        }
      >
        <TextSecondary>{<MdNewReleases />}</TextSecondary>
        <TextSecondary className="text-xs font-semibold">Hirings are open now</TextSecondary>
        <TextSecondary>{<MdNewReleases />}</TextSecondary>
      </div>

      <div className="flex flex-col items-center justify-center gap-5">
        <header>
          <TextPrimary className="text-center text-5xl font-bold">Make your ideas come to life</TextPrimary>
        </header>

        <p>
          <TextPrimary className="max-w-xl px-10 py-5 text-center text-sm md:p-0 md:text-lg">
            Hire talented freelancers from around the world or want to offer your expertise, this is where creativity
            meets opportunity
          </TextPrimary>
        </p>

        <div className="mt-10 flex flex-col items-center justify-center gap-7 md:mt-4 md:flex-row">
          <PrimaryButton className="gap-2 px-10 py-2 md:px-5">
            <div>Hire a freelancer</div>
            <MdOutlineAddShoppingCart />
          </PrimaryButton>

          <SecondaryButton className="gap-2 px-15 py-2 md:px-5">
            <IoMdPersonAdd />
            <div>Become one of us</div>
          </SecondaryButton>
        </div>
      </div>

      <div className="mt-5 flex items-center justify-center gap-2 p-2 text-center text-xs md:flex-row md:text-sm">
        <TextTertiary> 21+ Satisfied Customers</TextTertiary>
        <TextTertiary>
          <VerticalSeparator />
        </TextTertiary>
        <TextTertiary> 25+ Projects Delivered</TextTertiary>
        <TextTertiary>
          <VerticalSeparator />
        </TextTertiary>
        <TextTertiary> 15+ Talented Freelancers</TextTertiary>
      </div>

      <div className="relative mb-5 w-full overflow-hidden">
        <div className="animate-marquee flex whitespace-nowrap">
          {marqueeText}
          {marqueeText}
        </div>
        <div
          className={
            "pointer-events-none absolute top-0 left-0 h-full w-15 bg-gradient-to-r " +
            (isDark ? `from-[#0A0A0A] via-[#0A0A0A]` : `from-[#FFFFFF] via-[#FFFFFF]`) +
            " to-transparent lg:w-20"
          }
        />
        <div
          className={
            "pointer-events-none absolute top-0 right-0 h-full w-15 bg-gradient-to-l " +
            (isDark ? `from-[#0A0A0A] via-[#0A0A0A]` : `from-[#FFFFFF] via-[#FFFFFF]`) +
            " to-transparent lg:w-20"
          }
        />
      </div>
    </div>
  );
};
