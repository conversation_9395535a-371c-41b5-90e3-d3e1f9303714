import { PillButton } from "../../../Utils/Button";
import { useTheme } from "../../../../Context/Theme";

export const ThemeButton = () => {
  const { theme, setTheme } = useTheme();
  const isDark = theme === "dark";

  function toggle() {
    setTheme(isDark ? "light" : "dark");
    localStorage.setItem("theme", isDark ? "light" : "dark");
  }

  return <PillButton onClick={toggle} size={4} initialState={isDark} />;
};
