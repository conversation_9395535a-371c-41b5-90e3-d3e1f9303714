import { useTheme } from "../Context/Theme";
import { BackgroundColor } from "../constants";
import * as react_router from "react-router-dom";
import { Footer } from "../Components/Layout/Footer";
import { RestoreScroll } from "../Hooks/ScrollRestoration";
import { MobileNavbar } from "../Components/Layout/NavBar/Mobile";
import { DesktopNavbar } from "../Components/Layout/NavBar/Desktop";
import { ScrollToTopButton } from "../Components/Layout/ScrollToTopButton";

export const Root = () => {
  RestoreScroll();
  const { theme } = useTheme();

  return (
    <div className={`relative flex min-h-dvh flex-col bg-[${BackgroundColor[theme]}]`}>
      <DesktopNavbar />
      <MobileNavbar />

      <main className="relative z-0 my-17 flex-grow">
        <ScrollToTopButton />
        <react_router.Outlet />
      </main>

      <Footer />
    </div>
  );
};

// export const Root = () => {
//   RestoreScroll();
//   const { theme } = useTheme();
//   const isDark = theme === "dark";

//   return (
//     <div className={`relative flex min-h-screen flex-col ${isDark ? "bg-[#0A0A0A]" : "bg-[#FFFFFF]"}`}>
//       <main className="relative z-0 flex-1">
//         <div>
//           <DesktopNavbar />
//           <MobileNavbar />
//         </div>
//         <div className="relative my-15 flex-1 flex-col">
//           <ScrollToTopButton />
//           <react_router.Outlet />
//         </div>
//         <Footer />
//       </main>
//     </div>
//   );
// };
