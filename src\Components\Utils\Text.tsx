import React from "react";
import { useTheme } from "../../Context/Theme";

type TextProps<C extends React.ElementType = "div"> = { as?: C } & React.ComponentProps<C>;

const createTextComponent = (darkColor: string, lightColor: string) => {
  return function Text<C extends React.ElementType = "div">({ as, className, children, ...props }: TextProps<C>) {
    const { theme } = useTheme();
    const Component = as || "div";
    const color = theme === "dark" ? darkColor : lightColor;

    return (
      <Component className={`${color} ${className}`} {...props}>
        {children}
      </Component>
    );
  };
};

export const TextPrimary = createTextComponent("text-[#F0F0F0]", "text-[#171717]");
export const TextSecondary = createTextComponent("text-[#D0D0D0]", "text-[#2A2A2A]");
export const TextTertiary = createTextComponent("text-[#979797]", "text-[#454545]");
