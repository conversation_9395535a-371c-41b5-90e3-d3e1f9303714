import { useState, useEffect } from "react";
import { TextPrimary } from "../../Utils/Text";
import { useNavigate } from "react-router-dom";
import { Shortcuts } from "./helpers/Shortcuts";
import { useTheme } from "../../../Context/Theme";
import { BackgroundColor, tabs } from "../../../constants";

export const DesktopNavbar = () => {
  const { theme } = useTheme();
  const navigate = useNavigate();
  const isDark = theme === "dark";
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const toggleVisibility = () => setScrolled(window.scrollY > 0);
    window.addEventListener("scroll", toggleVisibility);
    return () => window.removeEventListener("scroll", toggleVisibility);
  }, []);

  const Logo = (
    <button onClick={() => navigate("/")} className="flex cursor-pointer items-center p-2 text-xl">
      <img src={`/images/logo-${theme}.png`} alt="logo" className="h-8 w-8" />
    </button>
  );

  const NavTabs = tabs.map((tab, index) => (
    <button
      key={index}
      onClick={() => navigate(`/${tab.toLowerCase()}`)}
      className={
        "flex cursor-pointer items-center rounded-lg px-2 py-1.5 " +
        (isDark ? "hover:bg-[#212121]" : "hover:bg-[#dedede]")
      }
    >
      <TextPrimary>{tab}</TextPrimary>
    </button>
  ));

  return (
    <div
      className={
        "fixed top-0 z-10 hidden h-12 w-full flex-row items-center justify-between p-7 md:flex " +
        `bg-[${BackgroundColor[theme]}]` +
        (scrolled && (isDark ? " border-b border-[#212121] shadow-xl" : " shadow-md"))
      }
    >
      <div className="flex flex-row items-center justify-center gap-2 text-sm">
        {Logo}
        {NavTabs}
      </div>

      <Shortcuts />
    </div>
  );
};
