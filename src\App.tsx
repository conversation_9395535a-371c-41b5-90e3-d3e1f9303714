import "./css/index.css";
import { StrictMode } from "react";
import { Root } from "./Layouts/Root";
import { _404Page } from "./Pages/404";
import { HomePage } from "./Pages/Home";
import * as Router from "react-router-dom";
import { createRoot } from "react-dom/client";
import { ThemeProvider } from "./Context/Theme";

const router = Router.createBrowserRouter([
  {
    path: "/",
    element: <Root />,
    children: [
      { path: "/", element: <HomePage /> },
      { path: "/404", element: <_404Page /> },
      { path: "/*", element: <Router.Navigate to="/404" replace /> },
    ],
  },
]);

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <ThemeProvider>
      <Router.RouterProvider router={router} />
    </ThemeProvider>
  </StrictMode>,
);
