import { twMerge } from "tailwind-merge";
import { useTheme } from "../../Context/Theme";
import { Dispatch, SetStateAction, useState } from "react";

const shape = "rounded-lg";
const text = "text-sm font-semibold";
const layout = "flex flex-row items-center justify-center";
const interactivity = "cursor-pointer transition-all duration-200 disabled:cursor-not-allowed";

const base = `${layout} ${shape} ${text} ${interactivity}`;

export const PrimaryButton = ({ children, className, ...props }: React.ButtonHTMLAttributes<HTMLButtonElement>) => {
  const { theme } = useTheme();
  const isDark = theme === "dark";

  const dark = "text-[#171717] bg-[#E5E5E5] hover:bg-[#D0D0D0]";
  const light = "text-[#FAFAFA] bg-[#212121] hover:bg-[#353535]";

  return (
    <button className={twMerge(`${base} ${isDark ? dark : light} `, className)} {...props}>
      {children}
    </button>
  );
};

export const SecondaryButton = ({ children, className, ...props }: React.ButtonHTMLAttributes<HTMLButtonElement>) => {
  const { theme } = useTheme();
  const isDark = theme === "dark";

  const dark = "text-[#E5E5E5] bg-[#212121] hover:bg-[#282828]";
  const light = "text-[#1A1A1A] bg-[#EDEDED] hover:bg-[#F5F5F5]";

  return (
    <button className={twMerge(`${base} ${isDark ? dark : light} `, className)} {...props}>
      {children}
    </button>
  );
};

export const TertiaryButton = ({ children, className, ...props }: React.ButtonHTMLAttributes<HTMLButtonElement>) => {
  const { theme } = useTheme();
  const isDark = theme === "dark";

  const dark = "text-[#FAFAFA] bg-[#212121] border-1 border-[#424242] hover:bg-[#282828]";
  const light = "text-[#050505] bg-[#FAFAFA] border-1 border-[#BDBDBD] hover:bg-[#E5E5E5]";

  return (
    <button className={twMerge(`${base} ${isDark ? dark : light} `, className)} {...props}>
      {children}
    </button>
  );
};

export const PillButton = ({
  size,
  state,
  onClick,
  className,
  initialState,
  ...props
}: React.ButtonHTMLAttributes<HTMLButtonElement> & {
  size?: number;
  initialState?: boolean;
  state?: [boolean, Dispatch<SetStateAction<boolean>>];
}) => {
  const { theme } = useTheme();
  const isDark = theme === "dark";
  const [on, setOn] = state || useState(!!initialState);

  const bh = size || 4;
  const bw = bh * 1.9;
  const p = bh * (20 / 100);

  const d = bh - 2 * p;

  return (
    <button
      onClick={(e) => {
        setOn(!on);
        onClick?.(e);
      }}
      style={{ height: `${bh * 4}px`, width: `${bw * 4}px`, padding: `${p * 4}px` }}
      className={`flex cursor-pointer items-center rounded-full outline-2 transition-colors duration-300 ${
        isDark ? "outline-[#dddddd]" : "outline-[#2a2a2a]"
      }`}
      {...props}
    >
      <div
        style={{
          height: `${d * 4}px`,
          width: `${d * 4}px`,
          transform: `translateX(${on ? (bw - p - d - p) * 4 : 0}px)`,
        }}
        className={`flex transform items-center justify-center rounded-full transition-transform duration-300 ${isDark ? "bg-[#FAFAFA]" : "bg-[#212121]"}`}
      ></div>
    </button>
  );
};
